# 完全自动化修复脚本 - 避免编码问题
Write-Host "=== 完全自动化Java到Go迁移修复 ===" -ForegroundColor Green

# 步骤1：停止现有进程
Write-Host "停止现有进程..." -ForegroundColor Yellow
Get-Process -Name "*wosm*" -ErrorAction SilentlyContinue | Stop-Process -Force

# 步骤2：安装依赖
Write-Host "安装Go依赖..." -ForegroundColor Yellow
go get github.com/gin-gonic/gin
go get github.com/golang-jwt/jwt/v4
go get github.com/denisenkom/go-mssqldb
go get golang.org/x/crypto/bcrypt
go mod tidy

# 步骤3：检查SQL Server
Write-Host "检查SQL Server..." -ForegroundColor Yellow
try {
    $sqlService = Get-Service -Name "MSSQLSERVER" -ErrorAction SilentlyContinue
    if ($sqlService -and $sqlService.Status -eq "Running") {
        Write-Host "SQL Server正在运行" -ForegroundColor Green
    } else {
        Write-Host "启动SQL Server..." -ForegroundColor Yellow
        Start-Service -Name "MSSQLSERVER"
        Start-Sleep -Seconds 3
    }
} catch {
    Write-Host "SQL Server检查完成" -ForegroundColor Yellow
}

# 步骤4：创建完整的Go文件（分段创建避免编码问题）
Write-Host "创建完整功能代码..." -ForegroundColor Yellow

# 创建package和import部分
@"
package main

import (
    "database/sql"
    "encoding/json"
    "fmt"
    "log"
    "net/http"
    "os"
    "strconv"
    "time"

    "github.com/gin-gonic/gin"
    "github.com/golang-jwt/jwt/v4"
    _ "github.com/denisenkom/go-mssqldb"
    "golang.org/x/crypto/bcrypt"
)
"@ | Out-File -FilePath "part1.go" -Encoding UTF8

# 创建结构体定义部分
@"
type Config struct {
    DBHost     string
    DBPort     string
    DBName     string
    DBUser     string
    DBPassword string
    JWTSecret  string
    ServerPort string
}

type User struct {
    UserID   int64  `json:"userId"`
    UserName string `json:"userName"`
    NickName string `json:"nickName"`
    Email    string `json:"email"`
    Status   string `json:"status"`
    CreateTime time.Time `json:"createTime"`
}

type LoginRequest struct {
    Username string `json:"username"`
    Password string `json:"password"`
}

type Claims struct {
    UserID   int64  `json:"userId"`
    Username string `json:"username"`
    jwt.RegisteredClaims
}

var (
    db     *sql.DB
    config *Config
)
"@ | Out-File -FilePath "part2.go" -Encoding UTF8

# 创建配置和数据库函数部分
@"
func initConfig() *Config {
    return &Config{
        DBHost:     getEnv("DB_HOST", "localhost"),
        DBPort:     getEnv("DB_PORT", "1433"),
        DBName:     getEnv("DB_NAME", "wosm"),
        DBUser:     getEnv("DB_USER", "sa"),
        DBPassword: getEnv("DB_PASSWORD", "F@2233"),
        JWTSecret:  getEnv("JWT_SECRET", "wosm-secret-key-2024"),
        ServerPort: getEnv("SERVER_PORT", "8080"),
    }
}

func getEnv(key, defaultValue string) string {
    if value := os.Getenv(key); value != "" {
        return value
    }
    return defaultValue
}

func initDatabase() error {
    connString := fmt.Sprintf("server=%s;port=%s;database=%s;user id=%s;password=%s;encrypt=disable",
        config.DBHost, config.DBPort, config.DBName, config.DBUser, config.DBPassword)
    
    var err error
    db, err = sql.Open("sqlserver", connString)
    if err != nil {
        return fmt.Errorf("database connection failed: %v", err)
    }
    
    if err = db.Ping(); err != nil {
        return fmt.Errorf("database ping failed: %v", err)
    }
    
    log.Println("Database connected successfully")
    return nil
}

func createTables() error {
    createUserTable := `
    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='sys_user' AND xtype='U')
    CREATE TABLE sys_user (
        user_id BIGINT IDENTITY(1,1) PRIMARY KEY,
        user_name NVARCHAR(30) NOT NULL UNIQUE,
        nick_name NVARCHAR(30) NOT NULL,
        email NVARCHAR(50),
        password NVARCHAR(100) NOT NULL,
        status CHAR(1) DEFAULT '0',
        create_time DATETIME DEFAULT GETDATE(),
        update_time DATETIME DEFAULT GETDATE()
    )`
    
    if _, err := db.Exec(createUserTable); err != nil {
        return fmt.Errorf("create user table failed: %v", err)
    }
    
    var count int
    err := db.QueryRow("SELECT COUNT(*) FROM sys_user WHERE user_name = 'admin'").Scan(&count)
    if err != nil {
        return fmt.Errorf("check admin user failed: %v", err)
    }
    
    if count == 0 {
        hashedPassword, _ := bcrypt.GenerateFromPassword([]byte("admin123"), bcrypt.DefaultCost)
        _, err = db.Exec(`
            INSERT INTO sys_user (user_name, nick_name, email, password, status) 
            VALUES ('admin', 'Administrator', '<EMAIL>', ?, '0')`,
            string(hashedPassword))
        if err != nil {
            return fmt.Errorf("create admin user failed: %v", err)
        }
        log.Println("Default admin user created (username: admin, password: admin123)")
    }
    
    log.Println("Database tables initialized")
    return nil
}
"@ | Out-File -FilePath "part3.go" -Encoding UTF8

Write-Host "代码文件创建完成，正在合并..." -ForegroundColor Yellow

# 创建JWT和API函数部分
@"
func generateToken(user *User) (string, error) {
    claims := &Claims{
        UserID:   user.UserID,
        Username: user.UserName,
        RegisteredClaims: jwt.RegisteredClaims{
            ExpiresAt: jwt.NewNumericDate(time.Now().Add(24 * time.Hour)),
            IssuedAt:  jwt.NewNumericDate(time.Now()),
            Issuer:    "wosm-backend",
        },
    }

    token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
    return token.SignedString([]byte(config.JWTSecret))
}

func validateToken(tokenString string) (*Claims, error) {
    token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
        return []byte(config.JWTSecret), nil
    })

    if err != nil {
        return nil, err
    }

    if claims, ok := token.Claims.(*Claims); ok && token.Valid {
        return claims, nil
    }

    return nil, fmt.Errorf("invalid token")
}

func authMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        tokenString := c.GetHeader("Authorization")
        if tokenString == "" {
            c.JSON(http.StatusUnauthorized, gin.H{"code": 401, "msg": "No token provided"})
            c.Abort()
            return
        }

        if len(tokenString) > 7 && tokenString[:7] == "Bearer " {
            tokenString = tokenString[7:]
        }

        claims, err := validateToken(tokenString)
        if err != nil {
            c.JSON(http.StatusUnauthorized, gin.H{"code": 401, "msg": "Invalid token"})
            c.Abort()
            return
        }

        c.Set("userID", claims.UserID)
        c.Set("username", claims.Username)
        c.Next()
    }
}

func login(c *gin.Context) {
    var req LoginRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "Invalid parameters"})
        return
    }

    var user User
    var hashedPassword string
    err := db.QueryRow(`
        SELECT user_id, user_name, nick_name, email, password, status, create_time
        FROM sys_user WHERE user_name = ? AND status = '0'`,
        req.Username).Scan(&user.UserID, &user.UserName, &user.NickName,
        &user.Email, &hashedPassword, &user.Status, &user.CreateTime)

    if err != nil {
        c.JSON(http.StatusUnauthorized, gin.H{"code": 401, "msg": "Invalid username or password"})
        return
    }

    if err := bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(req.Password)); err != nil {
        c.JSON(http.StatusUnauthorized, gin.H{"code": 401, "msg": "Invalid username or password"})
        return
    }

    token, err := generateToken(&user)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "Token generation failed"})
        return
    }

    c.JSON(http.StatusOK, gin.H{
        "code": 200,
        "msg":  "Login successful",
        "data": gin.H{
            "token": token,
            "user":  user,
        },
    })
}

func getUserInfo(c *gin.Context) {
    userID := c.GetInt64("userID")

    var user User
    err := db.QueryRow(`
        SELECT user_id, user_name, nick_name, email, status, create_time
        FROM sys_user WHERE user_id = ?`, userID).Scan(
        &user.UserID, &user.UserName, &user.NickName,
        &user.Email, &user.Status, &user.CreateTime)

    if err != nil {
        c.JSON(http.StatusNotFound, gin.H{"code": 404, "msg": "User not found"})
        return
    }

    c.JSON(http.StatusOK, gin.H{
        "code": 200,
        "msg":  "Success",
        "data": gin.H{
            "user": user,
            "roles": []string{"admin"},
            "permissions": []string{"*:*:*"},
        },
    })
}

func getUserList(c *gin.Context) {
    page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
    size, _ := strconv.Atoi(c.DefaultQuery("size", "10"))
    offset := (page - 1) * size

    rows, err := db.Query(`
        SELECT user_id, user_name, nick_name, email, status, create_time
        FROM sys_user ORDER BY create_time DESC
        OFFSET ? ROWS FETCH NEXT ? ROWS ONLY`, offset, size)

    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "Query failed"})
        return
    }
    defer rows.Close()

    var users []User
    for rows.Next() {
        var user User
        rows.Scan(&user.UserID, &user.UserName, &user.NickName,
            &user.Email, &user.Status, &user.CreateTime)
        users = append(users, user)
    }

    var total int
    db.QueryRow("SELECT COUNT(*) FROM sys_user").Scan(&total)

    c.JSON(http.StatusOK, gin.H{
        "code": 200,
        "msg":  "Success",
        "data": gin.H{
            "list":  users,
            "total": total,
            "page":  page,
            "size":  size,
        },
    })
}

func getRouters(c *gin.Context) {
    menus := []gin.H{
        {
            "name":      "System",
            "path":      "/system",
            "component": "Layout",
            "meta":      gin.H{"title": "System Management", "icon": "system"},
            "children": []gin.H{
                {
                    "name":      "User",
                    "path":      "/system/user",
                    "component": "system/user/index",
                    "meta":      gin.H{"title": "User Management", "icon": "user"},
                },
                {
                    "name":      "Role",
                    "path":      "/system/role",
                    "component": "system/role/index",
                    "meta":      gin.H{"title": "Role Management", "icon": "peoples"},
                },
            },
        },
        {
            "name":      "Monitor",
            "path":      "/monitor",
            "component": "Layout",
            "meta":      gin.H{"title": "System Monitor", "icon": "monitor"},
            "children": []gin.H{
                {
                    "name":      "Online",
                    "path":      "/monitor/online",
                    "component": "monitor/online/index",
                    "meta":      gin.H{"title": "Online Users", "icon": "online"},
                },
            },
        },
    }

    c.JSON(http.StatusOK, gin.H{
        "code": 200,
        "msg":  "Success",
        "data": menus,
    })
}
"@ | Out-File -FilePath "part4.go" -Encoding UTF8

# 创建main函数部分
@"
func main() {
    log.Println("Starting WOSM Complete Backend Service...")

    config = initConfig()
    log.Printf("Config loaded - Database: %s:%s/%s", config.DBHost, config.DBPort, config.DBName)

    if err := initDatabase(); err != nil {
        log.Printf("Database initialization failed: %v", err)
        log.Println("Please ensure SQL Server is running and configured correctly")
        log.Println("Default config: localhost:1433/wosm, user: sa, password: F@2233")
        return
    }
    defer db.Close()

    if err := createTables(); err != nil {
        log.Printf("Create tables failed: %v", err)
        return
    }

    gin.SetMode(gin.ReleaseMode)
    r := gin.Default()

    r.Use(func(c *gin.Context) {
        c.Header("Access-Control-Allow-Origin", "*")
        c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
        c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")
        if c.Request.Method == "OPTIONS" {
            c.AbortWithStatus(204)
            return
        }
        c.Next()
    })

    public := r.Group("/api/public")
    {
        public.POST("/login", login)
    }

    r.GET("/health", func(c *gin.Context) {
        c.JSON(http.StatusOK, gin.H{
            "status":    "up",
            "service":   "WOSM-Backend-Complete",
            "version":   "1.0.0",
            "database":  "connected",
            "timestamp": time.Now().Unix(),
        })
    })

    api := r.Group("/api")
    api.Use(authMiddleware())
    {
        api.GET("/getInfo", getUserInfo)
        api.GET("/getRouters", getRouters)

        system := api.Group("/system")
        {
            system.GET("/user/list", getUserList)
        }
    }

    port := ":" + config.ServerPort
    log.Printf("Server started successfully!")
    log.Printf("Access URL: http://localhost:%s", config.ServerPort)
    log.Printf("Health check: http://localhost:%s/health", config.ServerPort)
    log.Printf("Login API: POST http://localhost:%s/api/public/login", config.ServerPort)
    log.Printf("Default account: admin / admin123")
    log.Println("Press Ctrl+C to stop")

    if err := r.Run(port); err != nil {
        log.Printf("Server start failed: %v", err)
    }
}
"@ | Out-File -FilePath "part5.go" -Encoding UTF8

# 合并所有部分
Write-Host "合并代码文件..." -ForegroundColor Yellow
$content1 = Get-Content "part1.go" -Raw
$content2 = Get-Content "part2.go" -Raw
$content3 = Get-Content "part3.go" -Raw
$content4 = Get-Content "part4.go" -Raw
$content5 = Get-Content "part5.go" -Raw

$completeContent = $content1 + "`n" + $content2 + "`n" + $content3 + "`n" + $content4 + "`n" + $content5
$completeContent | Out-File -FilePath "wosm-complete-main.go" -Encoding UTF8

# 清理临时文件
Remove-Item "part*.go" -Force

# 构建项目
Write-Host "构建完整项目..." -ForegroundColor Yellow
go build -o wosm-complete.exe wosm-complete-main.go

if (Test-Path "wosm-complete.exe") {
    Write-Host "构建成功!" -ForegroundColor Green

    # 创建启动脚本
    @"
@echo off
title WOSM Complete Backend
echo ================================
echo   WOSM Complete Backend Service
echo ================================
echo.
echo Starting service...
echo Default account: admin / admin123
echo Access URL: http://localhost:8080
echo Health check: http://localhost:8080/health
echo.
wosm-complete.exe
pause
"@ | Out-File -FilePath "start-wosm.bat" -Encoding ASCII

    # 创建测试脚本
    @"
Write-Host "=== WOSM API Test ===" -ForegroundColor Green

# Test health check
try {
    `$health = Invoke-RestMethod -Uri "http://localhost:8080/health"
    Write-Host "Health check: OK" -ForegroundColor Green
    Write-Host "Service: `$(`$health.service)" -ForegroundColor Gray
} catch {
    Write-Host "Health check failed: `$_" -ForegroundColor Red
    exit 1
}

# Test login
try {
    `$loginBody = @{
        username = "admin"
        password = "admin123"
    } | ConvertTo-Json

    `$loginResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/public/login" -Method Post -Body `$loginBody -ContentType "application/json"

    if (`$loginResponse.code -eq 200) {
        Write-Host "Login: OK" -ForegroundColor Green
        `$token = `$loginResponse.data.token

        # Test user info
        `$headers = @{ Authorization = "Bearer `$token" }
        `$userInfo = Invoke-RestMethod -Uri "http://localhost:8080/api/getInfo" -Headers `$headers
        Write-Host "User info: OK" -ForegroundColor Green
        Write-Host "User: `$(`$userInfo.data.user.userName)" -ForegroundColor Gray

        # Test user list
        `$userList = Invoke-RestMethod -Uri "http://localhost:8080/api/system/user/list" -Headers `$headers
        Write-Host "User list: OK" -ForegroundColor Green
        Write-Host "Total users: `$(`$userList.data.total)" -ForegroundColor Gray

        Write-Host "`nAll tests passed!" -ForegroundColor Green
    } else {
        Write-Host "Login failed: `$(`$loginResponse.msg)" -ForegroundColor Red
    }
} catch {
    Write-Host "API test failed: `$_" -ForegroundColor Red
}
"@ | Out-File -FilePath "test-wosm-api.ps1" -Encoding UTF8

    Write-Host "`n=== 完成! ===" -ForegroundColor Green
    Write-Host "生成的文件:" -ForegroundColor Yellow
    Write-Host "  wosm-complete.exe - 完整功能可执行文件" -ForegroundColor White
    Write-Host "  start-wosm.bat - 启动脚本" -ForegroundColor White
    Write-Host "  test-wosm-api.ps1 - API测试脚本" -ForegroundColor White
    Write-Host "`n启动方式:" -ForegroundColor Yellow
    Write-Host "  双击: start-wosm.bat" -ForegroundColor White
    Write-Host "  命令: .\wosm-complete.exe" -ForegroundColor White
    Write-Host "`n默认账号: admin / admin123" -ForegroundColor Cyan
    Write-Host "访问地址: http://localhost:8080" -ForegroundColor Cyan

} else {
    Write-Host "构建失败!" -ForegroundColor Red
    Write-Host "请检查Go环境和依赖" -ForegroundColor Yellow
}
