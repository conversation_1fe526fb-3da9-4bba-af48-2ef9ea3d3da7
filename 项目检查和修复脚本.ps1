# Java到Go迁移项目检查和修复脚本
# 使用方法：在PowerShell中运行 .\项目检查和修复脚本.ps1

param(
    [string]$Action = "check"  # check, fix, build, test
)

# 设置项目路径
$ProjectPath = "D:\wosm\backend"
$JavaPath = "D:\wosm\ruoyi-java"

Write-Host "=== Java到Go迁移项目检查和修复工具 ===" -ForegroundColor Green
Write-Host "项目路径: $ProjectPath" -ForegroundColor Yellow

# 检查项目目录是否存在
if (-not (Test-Path $ProjectPath)) {
    Write-Host "错误: 项目目录不存在 $ProjectPath" -ForegroundColor Red
    exit 1
}

# 切换到项目目录
Set-Location $ProjectPath

function Check-Environment {
    Write-Host "`n=== 环境检查 ===" -ForegroundColor Cyan
    
    # 检查Go环境
    try {
        $goVersion = go version
        Write-Host "✅ Go环境: $goVersion" -ForegroundColor Green
    } catch {
        Write-Host "❌ Go环境未安装或未配置" -ForegroundColor Red
        return $false
    }
    
    # 检查项目文件
    $requiredFiles = @(
        "go.mod",
        "configs\config.yaml",
        "cmd\main.go",
        "internal\database\database.go"
    )
    
    foreach ($file in $requiredFiles) {
        if (Test-Path $file) {
            Write-Host "✅ 文件存在: $file" -ForegroundColor Green
        } else {
            Write-Host "❌ 文件缺失: $file" -ForegroundColor Red
        }
    }
    
    return $true
}

function Check-Dependencies {
    Write-Host "`n=== 依赖检查 ===" -ForegroundColor Cyan
    
    try {
        Write-Host "正在检查Go模块依赖..." -ForegroundColor Yellow
        go mod tidy
        Write-Host "✅ 依赖检查完成" -ForegroundColor Green
    } catch {
        Write-Host "❌ 依赖检查失败: $_" -ForegroundColor Red
        return $false
    }
    
    return $true
}

function Check-Database {
    Write-Host "`n=== 数据库连接检查 ===" -ForegroundColor Cyan
    
    # 检查SQL Server服务
    try {
        $sqlService = Get-Service -Name "MSSQLSERVER" -ErrorAction SilentlyContinue
        if ($sqlService -and $sqlService.Status -eq "Running") {
            Write-Host "✅ SQL Server服务运行中" -ForegroundColor Green
        } else {
            Write-Host "❌ SQL Server服务未运行" -ForegroundColor Red
        }
    } catch {
        Write-Host "⚠️  无法检查SQL Server服务状态" -ForegroundColor Yellow
    }
    
    # 检查数据库配置文件
    $configFile = "configs\config.yaml"
    if (Test-Path $configFile) {
        $config = Get-Content $configFile -Raw
        if ($config -match "localhost.*1433.*wosm") {
            Write-Host "✅ 数据库配置存在" -ForegroundColor Green
        } else {
            Write-Host "⚠️  数据库配置可能需要调整" -ForegroundColor Yellow
        }
    }
}

function Check-Redis {
    Write-Host "`n=== Redis连接检查 ===" -ForegroundColor Cyan
    
    # 检查Redis服务
    try {
        $redisProcess = Get-Process -Name "redis-server" -ErrorAction SilentlyContinue
        if ($redisProcess) {
            Write-Host "✅ Redis服务运行中" -ForegroundColor Green
        } else {
            Write-Host "❌ Redis服务未运行" -ForegroundColor Red
            Write-Host "提示: 请启动Redis服务或在配置中禁用Redis" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "⚠️  无法检查Redis服务状态" -ForegroundColor Yellow
    }
}

function Check-Code-Issues {
    Write-Host "`n=== 代码问题检查 ===" -ForegroundColor Cyan
    
    # 检查TODO标记
    $todoFiles = @()
    Get-ChildItem -Path "internal" -Recurse -Filter "*.go" | ForEach-Object {
        $content = Get-Content $_.FullName -Raw
        if ($content -match "TODO") {
            $todoFiles += $_.FullName
        }
    }
    
    if ($todoFiles.Count -gt 0) {
        Write-Host "⚠️  发现 $($todoFiles.Count) 个文件包含TODO标记:" -ForegroundColor Yellow
        $todoFiles | ForEach-Object { Write-Host "   - $_" -ForegroundColor Gray }
    } else {
        Write-Host "✅ 未发现TODO标记" -ForegroundColor Green
    }
    
    # 检查硬编码问题
    $hardcodedFiles = @()
    Get-ChildItem -Path "internal" -Recurse -Filter "*.go" | ForEach-Object {
        $content = Get-Content $_.FullName -Raw
        if ($content -match "F@2233|localhost:6379|localhost:1433") {
            $hardcodedFiles += $_.FullName
        }
    }
    
    if ($hardcodedFiles.Count -gt 0) {
        Write-Host "⚠️  发现 $($hardcodedFiles.Count) 个文件包含硬编码配置:" -ForegroundColor Yellow
        $hardcodedFiles | ForEach-Object { Write-Host "   - $_" -ForegroundColor Gray }
    } else {
        Write-Host "✅ 未发现硬编码问题" -ForegroundColor Green
    }
}

function Build-Project {
    Write-Host "`n=== 项目构建 ===" -ForegroundColor Cyan
    
    try {
        Write-Host "正在构建项目..." -ForegroundColor Yellow
        go build -o wosm-backend.exe cmd\main.go
        
        if (Test-Path "wosm-backend.exe") {
            Write-Host "✅ 项目构建成功" -ForegroundColor Green
            $fileInfo = Get-Item "wosm-backend.exe"
            Write-Host "   可执行文件大小: $([math]::Round($fileInfo.Length / 1MB, 2)) MB" -ForegroundColor Gray
            return $true
        } else {
            Write-Host "❌ 项目构建失败" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ 构建过程出错: $_" -ForegroundColor Red
        return $false
    }
}

function Test-Basic-Functionality {
    Write-Host "`n=== 基础功能测试 ===" -ForegroundColor Cyan
    
    if (-not (Test-Path "wosm-backend.exe")) {
        Write-Host "❌ 可执行文件不存在，请先构建项目" -ForegroundColor Red
        return $false
    }
    
    Write-Host "启动应用进行基础测试..." -ForegroundColor Yellow
    Write-Host "注意: 这将启动应用，请在另一个终端中测试API接口" -ForegroundColor Yellow
    Write-Host "测试完成后请按 Ctrl+C 停止应用" -ForegroundColor Yellow
    
    try {
        .\wosm-backend.exe
    } catch {
        Write-Host "应用已停止" -ForegroundColor Yellow
    }
}

function Fix-Common-Issues {
    Write-Host "`n=== 修复常见问题 ===" -ForegroundColor Cyan
    
    # 创建环境变量文件
    $envFile = ".env"
    if (-not (Test-Path $envFile)) {
        Write-Host "创建环境变量文件..." -ForegroundColor Yellow
        @"
# 数据库配置
DB_HOST=localhost
DB_PORT=1433
DB_NAME=wosm
DB_USERNAME=sa
DB_PASSWORD=F@2233

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT配置
JWT_SECRET=your-very-strong-jwt-secret-key-at-least-32-characters
JWT_EXPIRE=86400

# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8080

# 应用配置
APP_ENV=dev
LOG_LEVEL=debug
"@ | Out-File -FilePath $envFile -Encoding UTF8
        Write-Host "✅ 环境变量文件已创建: $envFile" -ForegroundColor Green
    }
    
    # 创建构建脚本
    $buildScript = "build.bat"
    if (-not (Test-Path $buildScript)) {
        Write-Host "创建构建脚本..." -ForegroundColor Yellow
        @"
@echo off
echo 构建Go应用...
go mod tidy
go build -o wosm-backend.exe cmd\main.go
if exist wosm-backend.exe (
    echo 构建完成！
    echo 可执行文件: wosm-backend.exe
) else (
    echo 构建失败！
)
pause
"@ | Out-File -FilePath $buildScript -Encoding ASCII
        Write-Host "✅ 构建脚本已创建: $buildScript" -ForegroundColor Green
    }
    
    # 创建启动脚本
    $startScript = "start.bat"
    if (-not (Test-Path $startScript)) {
        Write-Host "创建启动脚本..." -ForegroundColor Yellow
        @"
@echo off
echo 启动WOSM后端服务...
if not exist wosm-backend.exe (
    echo 可执行文件不存在，正在构建...
    call build.bat
)
echo 启动服务...
wosm-backend.exe
pause
"@ | Out-File -FilePath $startScript -Encoding ASCII
        Write-Host "✅ 启动脚本已创建: $startScript" -ForegroundColor Green
    }
}

function Show-Summary {
    Write-Host "`n=== 检查总结 ===" -ForegroundColor Cyan
    Write-Host "项目状态分析完成！" -ForegroundColor Green
    Write-Host ""
    Write-Host "下一步建议:" -ForegroundColor Yellow
    Write-Host "1. 如果发现问题，运行: .\项目检查和修复脚本.ps1 -Action fix" -ForegroundColor White
    Write-Host "2. 构建项目: .\项目检查和修复脚本.ps1 -Action build" -ForegroundColor White
    Write-Host "3. 测试应用: .\项目检查和修复脚本.ps1 -Action test" -ForegroundColor White
    Write-Host "4. 或者直接运行: .\build.bat 然后 .\start.bat" -ForegroundColor White
    Write-Host ""
    Write-Host "如需帮助，请查看: 迁移状况分析与落地方案.md" -ForegroundColor Cyan
}

# 主执行逻辑
switch ($Action.ToLower()) {
    "check" {
        Check-Environment
        Check-Dependencies
        Check-Database
        Check-Redis
        Check-Code-Issues
        Show-Summary
    }
    "fix" {
        Check-Environment
        Fix-Common-Issues
        Write-Host "`n✅ 常见问题修复完成！" -ForegroundColor Green
    }
    "build" {
        Check-Environment
        Check-Dependencies
        Build-Project
    }
    "test" {
        Test-Basic-Functionality
    }
    default {
        Write-Host "用法: .\项目检查和修复脚本.ps1 -Action [check|fix|build|test]" -ForegroundColor Yellow
        Write-Host "  check: 检查项目状态（默认）" -ForegroundColor White
        Write-Host "  fix:   修复常见问题" -ForegroundColor White
        Write-Host "  build: 构建项目" -ForegroundColor White
        Write-Host "  test:  测试应用" -ForegroundColor White
    }
}
