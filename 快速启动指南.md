# Java到Go迁移项目快速启动指南

## 🚀 快速开始

### 第一步：项目检查

在PowerShell中执行以下命令：

```powershell
# 进入项目目录
cd D:\wosm\backend

# 运行项目检查脚本
.\项目检查和修复脚本.ps1

# 或者手动检查
go version
go mod tidy
```

### 第二步：环境准备

#### 2.1 确认服务运行状态

**检查SQL Server 2012：**
```powershell
# 检查SQL Server服务
Get-Service -Name "MSSQLSERVER"

# 如果服务未运行，启动服务
Start-Service -Name "MSSQLSERVER"
```

**检查Redis（可选）：**
```powershell
# 如果安装了Redis，检查进程
Get-Process -Name "redis-server" -ErrorAction SilentlyContinue

# 如果没有Redis，项目会使用内存缓存
```

#### 2.2 修复常见问题

```powershell
# 运行修复脚本
.\项目检查和修复脚本.ps1 -Action fix
```

### 第三步：构建和运行

#### 3.1 构建项目

```powershell
# 方法1：使用脚本
.\项目检查和修复脚本.ps1 -Action build

# 方法2：手动构建
go build -o wosm-backend.exe cmd\main.go

# 方法3：使用生成的构建脚本
.\build.bat
```

#### 3.2 运行应用

```powershell
# 方法1：直接运行
.\wosm-backend.exe

# 方法2：使用启动脚本
.\start.bat

# 方法3：使用测试脚本
.\项目检查和修复脚本.ps1 -Action test
```

### 第四步：验证功能

#### 4.1 检查应用启动

应用启动后，您应该看到类似以下的日志输出：

```
{"level":"info","ts":**********.789,"caller":"main.go:24","msg":"服务启动中..."}
{"level":"info","ts":**********.790,"caller":"database.go:82","msg":"数据库连接成功"}
{"level":"info","ts":**********.791,"caller":"main.go:95","msg":"服务启动成功","port":8080}
```

#### 4.2 测试API接口

**健康检查：**
```powershell
# 使用curl（如果安装了）
curl http://localhost:8080/health

# 使用PowerShell
Invoke-RestMethod -Uri "http://localhost:8080/health" -Method Get
```

**预期响应：**
```json
{
  "status": "up",
  "service": "RuoYi-Go",
  "version": "1.0.0"
}
```

**登录接口测试：**
```powershell
# 使用PowerShell测试登录
$body = @{
    username = "admin"
    password = "admin123"
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:8080/api/public/login" -Method Post -Body $body -ContentType "application/json"
```

## 🔧 常见问题解决

### 问题1：编译错误

**错误信息：**
```
go: module requires Go 1.18 or later
```

**解决方案：**
```powershell
# 检查Go版本
go version

# 如果版本过低，请升级Go到1.18+
```

### 问题2：数据库连接失败

**错误信息：**
```
连接数据库失败: cannot connect to SQL Server
```

**解决方案：**
1. 确认SQL Server 2012服务运行
2. 检查数据库配置
3. 验证数据库用户权限

```powershell
# 检查SQL Server服务
Get-Service -Name "MSSQLSERVER"

# 启动服务（如果未运行）
Start-Service -Name "MSSQLSERVER"
```

### 问题3：端口占用

**错误信息：**
```
listen tcp :8080: bind: address already in use
```

**解决方案：**
```powershell
# 查找占用8080端口的进程
netstat -ano | findstr :8080

# 终止占用进程（替换PID）
taskkill /PID <PID> /F

# 或者修改配置文件中的端口
```

### 问题4：Redis连接失败

**错误信息：**
```
Redis连接失败: dial tcp 127.0.0.1:6379: connect: connection refused
```

**解决方案：**
项目已配置为Redis连接失败时使用内存缓存，这不会影响基本功能。

如需启用Redis：
1. 安装并启动Redis服务
2. 修改main.go中的Redis初始化代码

## 📋 功能验证清单

### 基础功能验证

- [ ] 应用成功启动
- [ ] 数据库连接正常
- [ ] 健康检查接口响应正常
- [ ] 日志输出正常

### 核心功能验证

- [ ] 用户登录功能
- [ ] JWT令牌生成
- [ ] 权限验证
- [ ] 用户信息获取
- [ ] 菜单路由获取

### API接口验证

**公开接口（无需认证）：**
- [ ] GET /health - 健康检查
- [ ] POST /api/public/login - 用户登录
- [ ] GET /api/public/captcha - 验证码获取

**认证接口（需要JWT令牌）：**
- [ ] GET /api/getInfo - 获取用户信息
- [ ] GET /api/getRouters - 获取用户菜单
- [ ] POST /api/logout - 用户登出

## 🎯 下一步计划

### 立即执行（今天）

1. **运行项目检查脚本**
2. **修复发现的问题**
3. **成功启动应用**
4. **验证基础功能**

### 短期计划（1-3天）

1. **完善TODO方法**
2. **添加错误处理**
3. **完善API文档**
4. **添加基础测试**

### 中期计划（1周内）

1. **生产环境配置**
2. **性能优化**
3. **安全加固**
4. **部署文档**

## 📞 获取帮助

如果在执行过程中遇到问题：

1. **查看详细分析：** `迁移状况分析与落地方案.md`
2. **检查日志输出：** 应用启动时的控制台输出
3. **运行诊断脚本：** `.\项目检查和修复脚本.ps1`
4. **查看配置文件：** `configs\config.yaml`

## 🎉 成功标志

当您看到以下情况时，说明迁移项目已成功运行：

1. ✅ 应用启动无错误
2. ✅ 数据库连接成功
3. ✅ 健康检查接口返回正常
4. ✅ 登录接口可以正常使用
5. ✅ 可以获取用户信息和菜单

恭喜！您的Java到Go迁移项目已经成功运行！
